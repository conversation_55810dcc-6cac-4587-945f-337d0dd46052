#!/usr/bin/env python3
################################################################################
# FILE: restart_app.py
# DESC: Quick restart script with updated validation
# USAGE: python3 restart_app.py
################################################################################

import sys
import os

def main():
    print("🔄 Restarting Integrated Application with Fixed Validation...")
    print("=" * 60)
    
    try:
        from app_integrated import main as app_main
        app_main()
    except Exception as e:
        print(f"❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

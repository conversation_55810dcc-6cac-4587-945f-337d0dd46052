#!/usr/bin/env bash
################################################################################
# FILE: fix_secrets.sh
# DESC: Remove secrets from git history and enable successful GitHub uploads
# USAGE: ./fix_secrets.sh
################################################################################

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "🔒 GitHub Secret Scanning Fix"
log_info "GitHub detected secrets in your repository and blocked the push."
log_info "This is a security feature to protect your API keys."
echo

# The problematic file from the error message
PROBLEM_FILE="68507cb0-f268-8008-898e-60359398f149.2025-06-17_203225.txt"

# Check if the file exists
if [[ -f "$PROBLEM_FILE" ]]; then
    log_warn "Found problematic file: $PROBLEM_FILE"
    log_info "This file contains API keys and needs to be removed."
    
    echo -n "Remove this file? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        rm "$PROBLEM_FILE"
        log_success "Removed $PROBLEM_FILE"
    else
        log_error "Cannot proceed without removing the file containing secrets."
        exit 1
    fi
else
    log_info "Problematic file not found in working directory."
fi

# Add to .gitignore to prevent future issues
log_info "Adding patterns to .gitignore to prevent future secret exposure..."

# Create or update .gitignore
cat >> .gitignore << 'EOF'

# Prevent accidental secret exposure
*.txt
.env
.env.local
.env.production
.env.development
*token*
*key*
*secret*
*password*
*credentials*

# Chat logs and temporary files
*chat*
*log*
*.log
68507cb0-f268-8008-898e-60359398f149*

EOF

log_success "Updated .gitignore"

# Remove the file from git history if it was committed
log_info "Removing secrets from git history..."
if git log --name-only --pretty=format: | grep -q "$PROBLEM_FILE" 2>/dev/null; then
    log_warn "File was committed to git history. Removing from history..."
    
    # Use git filter-branch to remove the file from history
    git filter-branch --force --index-filter \
        "git rm --cached --ignore-unmatch '$PROBLEM_FILE'" \
        --prune-empty --tag-name-filter cat -- --all
    
    log_success "Removed file from git history"
    
    # Clean up
    rm -rf .git/refs/original/
    git reflog expire --expire=now --all
    git gc --prune=now --aggressive
    
    log_success "Cleaned up git repository"
else
    log_info "File was not in git history"
fi

# Commit the .gitignore changes
log_info "Committing .gitignore updates..."
git add .gitignore
git commit -m "Add .gitignore rules to prevent secret exposure"

log_success "🎉 Secrets removed and .gitignore updated!"
echo
log_info "You can now safely upload to GitHub using:"
log_info "  ./github_upload_clean.sh"
echo
log_warn "⚠️  Important: Make sure to:"
log_warn "  1. Rotate any exposed API keys"
log_warn "  2. Check GitHub's security tab for any alerts"
log_warn "  3. Review files before committing in the future"

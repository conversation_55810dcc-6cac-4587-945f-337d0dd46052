# Ignore personal settings
config/settings.json

# Python
__pycache__/
*.py[cod]
*$py.class

# Virtual Environment
venv/
ENV/

# IDE
.idea/
.vscode/

# Project specific
output/

# Logs
*.log 
# Prevent accidental secret exposure
*.txt
.env
.env.local
.env.production
.env.development
*token*
*key*
*secret*
*password*
*credentials*

# Chat logs and temporary files
*chat*
*log*
*.log
68507cb0-f268-8008-898e-60359398f149*


#!/usr/bin/env bash
################################################################################
# FILE: fix_secrets_complete.sh
# DESC: Completely remove secrets from git history
# USAGE: ./fix_secrets_complete.sh
################################################################################

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "🔒 Complete Secret Removal from Git History"
echo

# Files that need to be removed from history
PROBLEM_FILES=(
    "68507cb0-f268-8008-898e-60359398f149.2025-06-17_203216.txt"
    "68507cb0-f268-8008-898e-60359398f149.2025-06-17_203225.txt"
    "postcard_lister_personal_access_token.txt"
)

log_info "Files to remove from git history:"
for file in "${PROBLEM_FILES[@]}"; do
    echo "  - $file"
done
echo

log_warn "⚠️  This will rewrite git history. This is safe for your local repo."
log_warn "⚠️  Make sure you have no important uncommitted changes."
echo -n "Continue? (y/N): "
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    log_info "Aborted."
    exit 0
fi

# Remove files from current working directory if they exist
for file in "${PROBLEM_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        rm "$file"
        log_success "Removed $file from working directory"
    fi
done

# Use git filter-repo if available, otherwise git filter-branch
if command -v git-filter-repo &> /dev/null; then
    log_info "Using git-filter-repo (recommended method)..."
    
    for file in "${PROBLEM_FILES[@]}"; do
        git filter-repo --path "$file" --invert-paths --force
    done
    
    log_success "Used git-filter-repo to clean history"
else
    log_info "Using git filter-branch (fallback method)..."
    
    # Create filter-branch command
    FILTER_CMD="git rm --cached --ignore-unmatch"
    for file in "${PROBLEM_FILES[@]}"; do
        FILTER_CMD="$FILTER_CMD '$file'"
    done
    
    # Run filter-branch
    git filter-branch --force --index-filter "$FILTER_CMD" --prune-empty --tag-name-filter cat -- --all
    
    # Clean up
    rm -rf .git/refs/original/
    git reflog expire --expire=now --all
    git gc --prune=now --aggressive
    
    log_success "Used git filter-branch to clean history"
fi

# Force update the current branch
git reset --hard HEAD

log_success "🎉 Git history cleaned!"
echo
log_info "Repository is now ready for upload."
log_info "Run: ./github_upload_clean.sh"
echo
log_warn "⚠️  Important reminders:"
log_warn "  1. Rotate any exposed API keys"
log_warn "  2. The git history has been rewritten"
log_warn "  3. This is normal and safe for removing secrets"

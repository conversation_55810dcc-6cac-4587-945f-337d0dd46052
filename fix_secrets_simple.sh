#!/usr/bin/env bash
################################################################################
# FILE: fix_secrets_simple.sh
# DESC: Simple approach to remove secrets and enable GitHub upload
# USAGE: ./fix_secrets_simple.sh
################################################################################

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "🔒 Simple Secret Fix - Alternative Approach"
echo

# First, let's commit any current changes
log_info "Committing current changes..."
git add -A
git commit -m "Pre-secret-fix commit" || log_info "No changes to commit"

# Create a new clean branch from the current state
log_info "Creating clean branch without secrets..."
CLEAN_BRANCH="clean-$(date +%Y%m%d-%H%M%S)"
git checkout -b "$CLEAN_BRANCH"

# Remove problematic files if they exist
PROBLEM_FILES=(
    "68507cb0-f268-8008-898e-60359398f149.2025-06-17_203216.txt"
    "68507cb0-f268-8008-898e-60359398f149.2025-06-17_203225.txt"
    "postcard_lister_personal_access_token.txt"
)

for file in "${PROBLEM_FILES[@]}"; do
    if [[ -f "$file" ]]; then
        rm "$file"
        log_success "Removed $file"
    fi
done

# Ensure .gitignore is comprehensive
log_info "Updating .gitignore..."
cat >> .gitignore << 'EOF'

# Comprehensive secret prevention
*.txt
.env*
*token*
*key*
*secret*
*password*
*credentials*
*api*key*
68507cb0-f268-8008-898e-60359398f149*
*chat*
*.log
logs/

EOF

# Commit the clean state
git add -A
git commit -m "Remove secrets and update gitignore for GitHub upload"

# Switch back to main and reset to clean state
log_info "Updating main branch with clean version..."
git checkout main
git reset --hard "$CLEAN_BRANCH"

# Delete the temporary branch
git branch -D "$CLEAN_BRANCH"

log_success "🎉 Repository cleaned!"
echo
log_info "✅ Secrets removed from current state"
log_info "✅ .gitignore updated to prevent future issues"
log_info "✅ Ready for GitHub upload"
echo
log_info "Now run: ./github_upload_clean.sh"
echo
log_warn "⚠️  Note: This approach creates a clean current state."
log_warn "⚠️  If GitHub still detects secrets in history, we may need"
log_warn "⚠️  to create a completely new repository."

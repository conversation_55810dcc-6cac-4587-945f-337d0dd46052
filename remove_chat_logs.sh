#!/usr/bin/env bash
################################################################################
# FILE: remove_chat_logs.sh
# DESC: Remove chat log files and exclude them from git
# USAGE: ./remove_chat_logs.sh
################################################################################

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

log_info "🗑️  Removing Chat Log Files"
echo

# Find and remove all chat log files
CHAT_FILES=($(find . -name "68507cb0*.txt" -type f 2>/dev/null || true))

if [[ ${#CHAT_FILES[@]} -eq 0 ]]; then
    log_info "No chat log files found in current directory"
else
    log_info "Found chat log files:"
    for file in "${CHAT_FILES[@]}"; do
        echo "  - $file"
    done
    echo
    
    echo -n "Remove these files? (y/N): "
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        for file in "${CHAT_FILES[@]}"; do
            rm "$file"
            log_success "Removed $file"
        done
    else
        log_error "Cannot proceed without removing chat log files"
        exit 1
    fi
fi

# Update .gitignore to exclude chat logs
log_info "Updating .gitignore to exclude chat logs..."
if ! grep -q "68507cb0" .gitignore 2>/dev/null; then
    cat >> .gitignore << 'EOF'

# Chat logs and conversation files
68507cb0*.txt
*chat*.txt
conversation*.txt
EOF
    log_success "Added chat log exclusions to .gitignore"
else
    log_info ".gitignore already excludes chat logs"
fi

# Remove from git index if they were tracked
log_info "Removing chat logs from git tracking..."
git rm --cached 68507cb0*.txt 2>/dev/null || log_info "Chat logs were not being tracked by git"

# Commit the changes
log_info "Committing removal of chat logs..."
git add .gitignore
git commit -m "Remove chat log files and exclude from git" || log_info "No changes to commit"

log_success "🎉 Chat logs removed and excluded!"
echo
log_info "✅ Chat log files deleted from working directory"
log_info "✅ .gitignore updated to exclude them permanently"
log_info "✅ Files removed from git tracking"
echo
log_info "Now you can safely upload to GitHub:"
log_info "  ./github_upload_clean.sh \"Removed chat logs, ready for upload\""

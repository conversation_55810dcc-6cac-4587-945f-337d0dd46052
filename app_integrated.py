#!/usr/bin/env python3
################################################################################
# FILE: app_integrated.py
# DESC: Fully integrated postcard lister using all core modules
# SPEC: Phase 1 - Core Integration Complete
################################################################################

import os
import sys
import json
import traceback
from pathlib import Path
from PyQt5.QtWidgets import (
    QApplication, QWidget, QTabWidget, QFormLayout, QLineEdit,
    QVBoxLayout, QHBoxLayout, QPushButton, Q<PERSON>abe<PERSON>, Q<PERSON><PERSON><PERSON><PERSON><PERSON>, 
    Q<PERSON><PERSON>tE<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont

# Import our sophisticated core modules
from core.vision_handler import get_postcard_metadata
from core.image_processor import process_image_set
from core.aws_uploader import upload_to_s3
from core.csv_generator import generate_csv, fill_row
from core.utils import is_image_file

################################################################################
# CONFIGURATION MANAGEMENT
################################################################################

class ConfigManager:
    """Handles loading and saving configuration from JSON files"""
    
    def __init__(self):
        self.config_path = "config/settings.json"
        self.template_path = "config/settings.template.json"
        self.config = {}
        self.load_config()
    
    def load_config(self):
        """Load configuration from settings.json, create from template if needed"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    self.config = json.load(f)
                print(f"✅ Loaded configuration from {self.config_path}")
            else:
                # Create from template
                if os.path.exists(self.template_path):
                    with open(self.template_path, 'r') as f:
                        self.config = json.load(f)
                    print(f"⚠️  Created config from template. Please edit {self.config_path}")
                else:
                    # Create minimal config
                    self.config = {
                        "aws_access_key": "",
                        "aws_secret_key": "",
                        "s3_bucket": "",
                        "aws_region": "us-east-1",
                        "openai_api_key": "",
                        "background_color": "#000000",
                        "custom_html": "",
                        "store_category_id": ""
                    }
                    print("⚠️  Created minimal configuration")
                
                self.save_config()
        except Exception as e:
            print(f"❌ Error loading config: {e}")
            self.config = {}
    
    def save_config(self):
        """Save current configuration to settings.json"""
        try:
            os.makedirs("config", exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=4)
            print(f"✅ Configuration saved to {self.config_path}")
        except Exception as e:
            print(f"❌ Error saving config: {e}")
    
    def get(self, key, default=""):
        """Get configuration value with default"""
        return self.config.get(key, default)
    
    def set(self, key, value):
        """Set configuration value"""
        self.config[key] = value
    
    def update_from_gui(self, gui_values):
        """Update config from GUI form values"""
        for key, value in gui_values.items():
            self.config[key] = value
        self.save_config()

################################################################################
# PROCESSING WORKER THREAD
################################################################################

class PostcardProcessor(QThread):
    """Background thread for processing postcards without blocking GUI"""
    
    # Signals for communicating with GUI
    progress_updated = pyqtSignal(int, str)  # progress percentage, status message
    processing_complete = pyqtSignal(dict)   # results dictionary
    error_occurred = pyqtSignal(str)         # error message
    
    def __init__(self, front_path, back_path, config):
        super().__init__()
        self.front_path = front_path
        self.back_path = back_path
        self.config = config
        self.results = {}
    
    def run(self):
        """Main processing pipeline - runs in background thread"""
        try:
            self.progress_updated.emit(10, "Starting image processing...")
            
            # Step 1: Process images (create multiple variants)
            output_dir = "output"
            processed_images = process_image_set(
                self.front_path, 
                self.back_path, 
                output_dir, 
                index=1,  # Could be made dynamic for batch processing
                bg_color=self.config.get("background_color", "#000000")
            )
            
            if not processed_images:
                raise Exception("Image processing failed")
            
            self.progress_updated.emit(30, "Images processed successfully")
            
            # Step 2: Extract metadata using AI vision
            vision_image_path = processed_images.get("vision")
            if not vision_image_path or not os.path.exists(vision_image_path):
                raise Exception("Vision image not created")
            
            self.progress_updated.emit(40, "Analyzing postcard with AI...")
            
            metadata = get_postcard_metadata(
                vision_image_path,
                self.config.get("openai_api_key", "")
            )
            
            if not metadata:
                raise Exception("AI metadata extraction failed")
            
            self.progress_updated.emit(70, "Metadata extracted successfully")
            
            # Step 3: Upload images to S3 (if configured)
            s3_urls = {}
            if (self.config.get("aws_access_key") and 
                self.config.get("aws_secret_key") and 
                self.config.get("s3_bucket")):
                
                self.progress_updated.emit(80, "Uploading images to S3...")
                
                # Upload front and back images
                for img_type in ["front", "back", "final"]:
                    if img_type in processed_images:
                        img_path = processed_images[img_type]
                        if os.path.exists(img_path):
                            url = upload_to_s3(
                                img_path,
                                self.config.get("s3_bucket"),
                                f"postcard_{metadata.get('Title', 'untitled')}",
                                self.config.get("aws_access_key"),
                                self.config.get("aws_secret_key"),
                                self.config.get("aws_region", "us-east-1"),
                                self.config.get("s3_base_url", "")
                            )
                            if url:
                                s3_urls[img_type] = url
                
                self.progress_updated.emit(90, "Images uploaded to S3")
            else:
                self.progress_updated.emit(90, "S3 upload skipped (not configured)")
            
            # Step 4: Compile results
            self.results = {
                "metadata": metadata,
                "processed_images": processed_images,
                "s3_urls": s3_urls,
                "front_path": self.front_path,
                "back_path": self.back_path
            }
            
            self.progress_updated.emit(100, "Processing complete!")
            self.processing_complete.emit(self.results)
            
        except Exception as e:
            error_msg = f"Processing failed: {str(e)}\n{traceback.format_exc()}"
            self.error_occurred.emit(error_msg)

################################################################################
# MAIN APPLICATION CLASS
################################################################################

class IntegratedPostcardLister(QWidget):
    """Main application window with full integration"""
    
    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.processor = None
        self.results_data = []
        
        self.init_ui()
        self.load_config_to_gui()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Postcard Lister - Integrated Version")
        self.setGeometry(100, 100, 1000, 700)
        
        # Create tab widget
        self.tabs = QTabWidget()
        
        # Create tabs
        self.settings_tab = self.create_settings_tab()
        self.process_tab = self.create_process_tab()
        self.results_tab = self.create_results_tab()
        
        self.tabs.addTab(self.settings_tab, "⚙️ Settings")
        self.tabs.addTab(self.process_tab, "🔄 Process")
        self.tabs.addTab(self.results_tab, "📊 Results")
        
        # Main layout
        layout = QVBoxLayout()
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_settings_tab(self):
        """Create the settings configuration tab"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # Configuration form
        form_group = QGroupBox("Configuration")
        form_layout = QFormLayout()
        
        # Create form fields
        self.openai_key_field = QLineEdit()
        self.openai_key_field.setEchoMode(QLineEdit.Password)
        
        self.aws_access_key_field = QLineEdit()
        self.aws_secret_key_field = QLineEdit()
        self.aws_secret_key_field.setEchoMode(QLineEdit.Password)
        
        self.s3_bucket_field = QLineEdit()
        self.aws_region_field = QLineEdit()
        self.store_category_field = QLineEdit()
        self.background_color_field = QLineEdit()
        
        # Add fields to form
        form_layout.addRow("OpenAI API Key:", self.openai_key_field)
        form_layout.addRow("AWS Access Key:", self.aws_access_key_field)
        form_layout.addRow("AWS Secret Key:", self.aws_secret_key_field)
        form_layout.addRow("S3 Bucket:", self.s3_bucket_field)
        form_layout.addRow("AWS Region:", self.aws_region_field)
        form_layout.addRow("Store Category ID:", self.store_category_field)
        form_layout.addRow("Background Color:", self.background_color_field)
        
        form_group.setLayout(form_layout)
        
        # Save button
        save_button = QPushButton("💾 Save Configuration")
        save_button.clicked.connect(self.save_config_from_gui)
        
        # Status display
        self.config_status = QTextEdit()
        self.config_status.setMaximumHeight(100)
        self.config_status.setReadOnly(True)
        
        layout.addWidget(form_group)
        layout.addWidget(save_button)
        layout.addWidget(QLabel("Configuration Status:"))
        layout.addWidget(self.config_status)
        
        tab.setLayout(layout)
        return tab

    def create_process_tab(self):
        """Create the main processing tab"""
        tab = QWidget()
        layout = QVBoxLayout()

        # File selection group
        file_group = QGroupBox("Select Postcard Images")
        file_layout = QVBoxLayout()

        # Front image selection
        front_layout = QHBoxLayout()
        self.front_path_label = QLabel("No front image selected")
        self.front_select_btn = QPushButton("📁 Select Front Image")
        self.front_select_btn.clicked.connect(self.select_front_image)
        front_layout.addWidget(QLabel("Front:"))
        front_layout.addWidget(self.front_path_label)
        front_layout.addWidget(self.front_select_btn)

        # Back image selection
        back_layout = QHBoxLayout()
        self.back_path_label = QLabel("No back image selected")
        self.back_select_btn = QPushButton("📁 Select Back Image")
        self.back_select_btn.clicked.connect(self.select_back_image)
        back_layout.addWidget(QLabel("Back:"))
        back_layout.addWidget(self.back_path_label)
        back_layout.addWidget(self.back_select_btn)

        file_layout.addLayout(front_layout)
        file_layout.addLayout(back_layout)
        file_group.setLayout(file_layout)

        # Processing controls
        process_group = QGroupBox("Processing")
        process_layout = QVBoxLayout()

        # Process button
        self.process_btn = QPushButton("🚀 Process Postcard")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setEnabled(False)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("Ready to process")

        process_layout.addWidget(self.process_btn)
        process_layout.addWidget(self.progress_label)
        process_layout.addWidget(self.progress_bar)
        process_group.setLayout(process_layout)

        # Output log
        log_group = QGroupBox("Processing Log")
        log_layout = QVBoxLayout()

        self.process_log = QTextEdit()
        self.process_log.setReadOnly(True)
        self.process_log.setMaximumHeight(200)

        log_layout.addWidget(self.process_log)
        log_group.setLayout(log_layout)

        # Add all groups to main layout
        layout.addWidget(file_group)
        layout.addWidget(process_group)
        layout.addWidget(log_group)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_results_tab(self):
        """Create the results display tab"""
        tab = QWidget()
        layout = QVBoxLayout()

        # Results display
        results_group = QGroupBox("Processing Results")
        results_layout = QVBoxLayout()

        self.results_display = QTextEdit()
        self.results_display.setReadOnly(True)

        results_layout.addWidget(self.results_display)
        results_group.setLayout(results_layout)

        # Export controls
        export_group = QGroupBox("Export")
        export_layout = QHBoxLayout()

        self.export_csv_btn = QPushButton("📄 Export to CSV")
        self.export_csv_btn.clicked.connect(self.export_to_csv)
        self.export_csv_btn.setEnabled(False)

        self.view_images_btn = QPushButton("🖼️ View Processed Images")
        self.view_images_btn.clicked.connect(self.view_processed_images)
        self.view_images_btn.setEnabled(False)

        export_layout.addWidget(self.export_csv_btn)
        export_layout.addWidget(self.view_images_btn)
        export_layout.addStretch()

        export_group.setLayout(export_layout)

        layout.addWidget(results_group)
        layout.addWidget(export_group)

        tab.setLayout(layout)
        return tab

    def load_config_to_gui(self):
        """Load configuration values into GUI fields"""
        self.openai_key_field.setText(self.config_manager.get("openai_api_key"))
        self.aws_access_key_field.setText(self.config_manager.get("aws_access_key"))
        self.aws_secret_key_field.setText(self.config_manager.get("aws_secret_key"))
        self.s3_bucket_field.setText(self.config_manager.get("s3_bucket"))
        self.aws_region_field.setText(self.config_manager.get("aws_region", "us-east-1"))
        self.store_category_field.setText(self.config_manager.get("store_category_id"))
        self.background_color_field.setText(self.config_manager.get("background_color", "#000000"))

        self.update_config_status()

    def save_config_from_gui(self):
        """Save GUI values to configuration"""
        gui_values = {
            "openai_api_key": self.openai_key_field.text(),
            "aws_access_key": self.aws_access_key_field.text(),
            "aws_secret_key": self.aws_secret_key_field.text(),
            "s3_bucket": self.s3_bucket_field.text(),
            "aws_region": self.aws_region_field.text(),
            "store_category_id": self.store_category_field.text(),
            "background_color": self.background_color_field.text()
        }

        self.config_manager.update_from_gui(gui_values)
        self.update_config_status()
        self.log_message("✅ Configuration saved successfully")

    def update_config_status(self):
        """Update the configuration status display"""
        status_lines = []

        # Check OpenAI API key
        if self.config_manager.get("openai_api_key"):
            status_lines.append("✅ OpenAI API key configured")
        else:
            status_lines.append("❌ OpenAI API key missing")

        # Check AWS configuration
        if (self.config_manager.get("aws_access_key") and
            self.config_manager.get("aws_secret_key") and
            self.config_manager.get("s3_bucket")):
            status_lines.append("✅ AWS S3 configured")
        else:
            status_lines.append("⚠️ AWS S3 not configured (optional)")

        # Check store category
        if self.config_manager.get("store_category_id"):
            status_lines.append("✅ Store category configured")
        else:
            status_lines.append("⚠️ Store category not set")

        self.config_status.setText("\n".join(status_lines))

    def select_front_image(self):
        """Select front image file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Front Image",
            "",
            "Images (*.jpg *.jpeg *.png)"
        )
        if file_path:
            self.front_path = file_path
            self.front_path_label.setText(os.path.basename(file_path))
            self.log_message(f"✅ Front image selected: {os.path.basename(file_path)}")
            self.check_ready_to_process()

    def select_back_image(self):
        """Select back image file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Back Image",
            "",
            "Images (*.jpg *.jpeg *.png)"
        )
        if file_path:
            self.back_path = file_path
            self.back_path_label.setText(os.path.basename(file_path))
            self.log_message(f"✅ Back image selected: {os.path.basename(file_path)}")
            self.check_ready_to_process()

    def check_ready_to_process(self):
        """Check if we have everything needed to start processing"""
        has_images = hasattr(self, 'front_path') and hasattr(self, 'back_path')
        has_openai_key = bool(self.config_manager.get("openai_api_key"))

        ready = has_images and has_openai_key
        self.process_btn.setEnabled(ready)

        if ready:
            self.progress_label.setText("Ready to process postcard")
        elif not has_images:
            self.progress_label.setText("Please select both front and back images")
        elif not has_openai_key:
            self.progress_label.setText("Please configure OpenAI API key in Settings")

    def start_processing(self):
        """Start the postcard processing in background thread"""
        if not hasattr(self, 'front_path') or not hasattr(self, 'back_path'):
            self.log_message("❌ Please select both front and back images")
            return

        if not self.config_manager.get("openai_api_key"):
            self.log_message("❌ Please configure OpenAI API key in Settings tab")
            return

        # Disable process button during processing
        self.process_btn.setEnabled(False)
        self.progress_bar.setValue(0)

        # Create and start processor thread
        self.processor = PostcardProcessor(
            self.front_path,
            self.back_path,
            self.config_manager.config
        )

        # Connect signals
        self.processor.progress_updated.connect(self.update_progress)
        self.processor.processing_complete.connect(self.processing_finished)
        self.processor.error_occurred.connect(self.processing_error)

        # Start processing
        self.processor.start()
        self.log_message("🚀 Starting postcard processing...")

    def update_progress(self, percentage, message):
        """Update progress bar and status message"""
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(message)
        self.log_message(f"[{percentage}%] {message}")

    def processing_finished(self, results):
        """Handle successful processing completion"""
        self.results_data.append(results)

        # Re-enable process button
        self.process_btn.setEnabled(True)
        self.export_csv_btn.setEnabled(True)
        self.view_images_btn.setEnabled(True)

        # Display results
        self.display_results(results)

        # Switch to results tab
        self.tabs.setCurrentIndex(2)

        self.log_message("🎉 Processing completed successfully!")

    def processing_error(self, error_message):
        """Handle processing errors"""
        self.process_btn.setEnabled(True)
        self.progress_bar.setValue(0)
        self.progress_label.setText("Processing failed")

        self.log_message(f"❌ Processing failed: {error_message}")

    def display_results(self, results):
        """Display processing results in the results tab"""
        metadata = results.get("metadata", {})
        s3_urls = results.get("s3_urls", {})

        # Format results for display
        display_text = "🎯 POSTCARD ANALYSIS RESULTS\n"
        display_text += "=" * 50 + "\n\n"

        # Basic metadata
        display_text += "📋 METADATA:\n"
        for key, value in metadata.items():
            if value:  # Only show non-empty values
                display_text += f"  {key}: {value}\n"

        display_text += "\n"

        # Image processing results
        processed_images = results.get("processed_images", {})
        display_text += "🖼️ PROCESSED IMAGES:\n"
        for img_type, path in processed_images.items():
            display_text += f"  {img_type.title()}: {os.path.basename(path)}\n"

        display_text += "\n"

        # S3 URLs if available
        if s3_urls:
            display_text += "☁️ S3 UPLOAD URLS:\n"
            for img_type, url in s3_urls.items():
                display_text += f"  {img_type.title()}: {url}\n"
        else:
            display_text += "⚠️ S3 upload not configured or failed\n"

        display_text += "\n"
        display_text += "✅ Ready for CSV export!"

        self.results_display.setText(display_text)

    def export_to_csv(self):
        """Export results to eBay-compatible CSV"""
        if not self.results_data:
            self.log_message("❌ No results to export")
            return

        try:
            # Get save location
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save CSV File",
                "postcard_listings.csv",
                "CSV Files (*.csv)"
            )

            if not file_path:
                return

            # Prepare data for CSV generation
            csv_rows = []
            for result in self.results_data:
                metadata = result.get("metadata", {})
                s3_urls = result.get("s3_urls", {})

                # Create row data (metadata, s3_urls, settings)
                row_data = (metadata, s3_urls, self.config_manager.config)
                csv_rows.append(row_data)

            # Generate CSV using our core module
            template_path = "data/postcard-ebay-template-csv-version.csv"
            if os.path.exists(template_path):
                generate_csv(file_path, template_path, csv_rows)
                self.log_message(f"✅ CSV exported successfully: {file_path}")
            else:
                self.log_message(f"❌ Template file not found: {template_path}")

        except Exception as e:
            self.log_message(f"❌ CSV export failed: {str(e)}")

    def view_processed_images(self):
        """Open the output directory to view processed images"""
        output_dir = "output"
        if os.path.exists(output_dir):
            if sys.platform == "win32":
                os.startfile(output_dir)
            elif sys.platform == "darwin":
                os.system(f"open {output_dir}")
            else:
                os.system(f"xdg-open {output_dir}")

            self.log_message(f"📁 Opened output directory: {output_dir}")
        else:
            self.log_message("❌ Output directory not found")

    def log_message(self, message):
        """Add message to process log"""
        self.process_log.append(message)
        print(message)  # Also print to console

################################################################################
# MAIN ENTRY POINT
################################################################################

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Postcard Lister - Integrated")
    app.setApplicationVersion("1.0")

    # Create and show main window
    window = IntegratedPostcardLister()
    window.show()

    # Start event loop
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
################################################################################
# FILE: test_github_catalog.py
# DESC: Test script for GitHub catalog system
# USAGE: python3 test_github_catalog.py
################################################################################

import os
import json
from pathlib import Path

def test_github_catalog_imports():
    """Test that GitHub catalog modules can be imported"""
    print("🧪 Testing GitHub Catalog Imports...")
    
    try:
        from core.github_catalog import GitHubCatalog, upload_product_to_github
        print("✅ core.github_catalog - OK")
        return True
    except Exception as e:
        print(f"❌ core.github_catalog - FAILED: {e}")
        return False

def test_configuration_structure():
    """Test new configuration structure"""
    print("\n🧪 Testing Configuration Structure...")
    
    try:
        # Check template
        with open("config/settings.template.json", "r") as f:
            template = json.load(f)
        
        required_fields = [
            "openai_api_key",
            "github_token", 
            "github_owner",
            "github_repo",
            "use_multi_llm",
            "use_github_catalog"
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in template:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing fields in template: {missing_fields}")
            return False
        
        print("✅ Configuration template has all required fields")
        
        # Check current config
        with open("config/settings.json", "r") as f:
            current = json.load(f)
        
        # Should not have AWS fields anymore
        aws_fields = ["aws_access_key", "aws_secret_key", "s3_bucket"]
        aws_found = [field for field in aws_fields if field in current]
        
        if aws_found:
            print(f"⚠️ Old AWS fields still present: {aws_found}")
        else:
            print("✅ AWS fields removed from configuration")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_directory_structure():
    """Test catalog directory structure"""
    print("\n🧪 Testing Directory Structure...")
    
    required_dirs = [
        "catalog",
        "catalog/products",
        "catalog/products/solar-panels",
        "catalog/products/postcards",
        "catalog/products/electronics",
        "catalog/exports",
        "catalog/templates",
        "docs"
    ]
    
    missing_dirs = []
    for directory in required_dirs:
        if not os.path.exists(directory):
            missing_dirs.append(directory)
    
    if missing_dirs:
        print(f"❌ Missing directories: {missing_dirs}")
        return False
    
    print("✅ All catalog directories present")
    return True

def test_cleanup_results():
    """Test that cleanup was successful"""
    print("\n🧪 Testing Cleanup Results...")
    
    # Check that redundant files were removed
    redundant_patterns = [
        "prf_github_one_shot_uploader_v",
        "app_gh_init_v",
        "fix_secrets",
        "force_",
        "push_to_",
        "resolve_",
        "remove_"
    ]
    
    current_files = os.listdir(".")
    redundant_found = []
    
    for pattern in redundant_patterns:
        for file in current_files:
            if pattern in file and file.endswith(('.sh', '.py')):
                redundant_found.append(file)
    
    if redundant_found:
        print(f"⚠️ Some redundant files still present: {redundant_found[:5]}...")
    else:
        print("✅ Redundant files cleaned up")
    
    # Check that archive directory exists
    if os.path.exists("archive"):
        print("✅ Archive directory created")
    else:
        print("❌ Archive directory missing")
        return False
    
    return True

def test_integrated_app_updates():
    """Test that integrated app was updated for GitHub"""
    print("\n🧪 Testing Integrated App Updates...")
    
    try:
        # Check imports
        with open("app_integrated.py", "r") as f:
            content = f.read()
        
        # Should import GitHub catalog
        if "from core.github_catalog import" in content:
            print("✅ GitHub catalog import added")
        else:
            print("❌ GitHub catalog import missing")
            return False
        
        # Should not import AWS uploader
        if "from core.aws_uploader import" in content:
            print("⚠️ AWS uploader import still present")
        else:
            print("✅ AWS uploader import removed")
        
        # Should have GitHub-related GUI elements
        if "github_token_field" in content:
            print("✅ GitHub token field added to GUI")
        else:
            print("❌ GitHub token field missing from GUI")
            return False
        
        # Should use catalog_urls instead of s3_urls
        if "catalog_urls" in content:
            print("✅ Catalog URLs used in results")
        else:
            print("❌ Catalog URLs missing from results")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ App update test failed: {e}")
        return False

def test_requirements_updated():
    """Test that requirements were updated"""
    print("\n🧪 Testing Requirements Updates...")
    
    try:
        with open("requirements.txt", "r") as f:
            requirements = f.read()
        
        # Should have requests for GitHub API
        if "requests" in requirements:
            print("✅ requests library added")
        else:
            print("❌ requests library missing")
            return False
        
        # Should not have boto3 anymore
        if "boto3" in requirements:
            print("⚠️ boto3 still in requirements (can be removed)")
        else:
            print("✅ boto3 removed from requirements")
        
        return True
        
    except Exception as e:
        print(f"❌ Requirements test failed: {e}")
        return False

def test_security_improvements():
    """Test security improvements"""
    print("\n🧪 Testing Security Improvements...")
    
    try:
        # Check .gitignore
        if os.path.exists(".gitignore"):
            with open(".gitignore", "r") as f:
                gitignore = f.read()
            
            security_patterns = [
                "*.env",
                "**/config/settings.json",
                "**/*api_key*",
                "**/*token*",
                "68507cb0*.txt"
            ]
            
            missing_patterns = []
            for pattern in security_patterns:
                if pattern not in gitignore:
                    missing_patterns.append(pattern)
            
            if missing_patterns:
                print(f"⚠️ Missing security patterns in .gitignore: {missing_patterns}")
            else:
                print("✅ Security patterns added to .gitignore")
        
        # Check that API key is not exposed in config
        with open("config/settings.json", "r") as f:
            config = json.load(f)
        
        openai_key = config.get("openai_api_key", "")
        if openai_key.startswith("sk-") and len(openai_key) > 50:
            print("❌ SECURITY RISK: Real API key in config file!")
            return False
        else:
            print("✅ No exposed API keys in config")
        
        return True
        
    except Exception as e:
        print(f"❌ Security test failed: {e}")
        return False

def main():
    """Run all GitHub catalog tests"""
    print("🧪 GITHUB CATALOG SYSTEM TESTS")
    print("=" * 60)
    
    tests = [
        ("GitHub Catalog Imports", test_github_catalog_imports),
        ("Configuration Structure", test_configuration_structure),
        ("Directory Structure", test_directory_structure),
        ("Cleanup Results", test_cleanup_results),
        ("Integrated App Updates", test_integrated_app_updates),
        ("Requirements Updates", test_requirements_updated),
        ("Security Improvements", test_security_improvements)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n{'='*60}")
    print(f"🎯 GITHUB CATALOG TEST RESULTS: {passed}/{total} PASSED")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - GitHub catalog system ready!")
        print("\n✅ Transformation Complete:")
        print("  🧹 Codebase cleaned up")
        print("  🔒 Security improved")
        print("  🌐 GitHub catalog implemented")
        print("  ⚙️ Configuration simplified")
        print("  🎨 UX enhanced")
        print("\n🚀 Ready to run: python3 run_integrated.py")
        return True
    else:
        print(f"❌ {total - passed} tests failed - Please review issues")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

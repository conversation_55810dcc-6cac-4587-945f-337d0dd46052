#!/usr/bin/env python3
################################################################################
# FILE: core/github_catalog.py
# DESC: GitHub-based catalog system for product storage and management
# FEAT: CRUD operations, image hosting, catalog management via GitHub API
################################################################################

import os
import json
import base64
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import subprocess

class GitHubCatalog:
    """GitHub-based catalog system for product management"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.github_token = self._get_github_token()
        self.repo_owner = self._get_repo_info()[0]
        self.repo_name = self._get_repo_info()[1]
        self.api_base = "https://api.github.com"
        
    def _get_github_token(self) -> str:
        """Get GitHub token from various sources"""
        # Try config first
        token = self.config.get("github_token")
        if token and not token.startswith("YOUR_"):
            return token
            
        # Try environment variables
        token = os.environ.get("GITHUB_TOKEN") or os.environ.get("GH_TOKEN")
        if token:
            return token
            
        # Try gh CLI
        try:
            result = subprocess.run(
                ["gh", "auth", "token"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            return result.stdout.strip()
        except:
            pass
            
        raise Exception("GitHub token not found. Please configure or run 'gh auth login'")
    
    def _get_repo_info(self) -> Tuple[str, str]:
        """Get repository owner and name from git remote"""
        try:
            result = subprocess.run(
                ["git", "remote", "get-url", "origin"],
                capture_output=True,
                text=True,
                check=True
            )
            
            remote_url = result.stdout.strip()
            
            # Parse GitHub URL (https or ssh format)
            if "github.com" in remote_url:
                if remote_url.startswith("git@"):
                    # SSH format: **************:owner/repo.git
                    parts = remote_url.split(":")[-1].replace(".git", "").split("/")
                else:
                    # HTTPS format: https://github.com/owner/repo.git
                    parts = remote_url.split("/")[-2:]
                    parts[-1] = parts[-1].replace(".git", "")
                
                return parts[0], parts[1]
                
        except Exception as e:
            print(f"Could not determine repo info: {e}")
            
        # Fallback to config or defaults
        return (
            self.config.get("github_owner", "your-username"),
            self.config.get("github_repo", "postcard-lister")
        )
    
    def _make_api_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make authenticated GitHub API request"""
        url = f"{self.api_base}{endpoint}"
        headers = {
            "Authorization": f"token {self.github_token}",
            "Accept": "application/vnd.github.v3+json",
            "Content-Type": "application/json"
        }
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(url, headers=headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json() if response.content else {}
            
        except requests.exceptions.RequestException as e:
            print(f"GitHub API request failed: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"Response: {e.response.text}")
            raise
    
    def upload_image_to_repo(self, image_path: str, catalog_path: str) -> str:
        """Upload image to GitHub repository and return URL"""
        try:
            # Read and encode image
            with open(image_path, "rb") as f:
                image_data = base64.b64encode(f.read()).decode()
            
            # Create commit message
            commit_message = f"Add product image: {os.path.basename(catalog_path)}"
            
            # Check if file exists (for updates)
            file_sha = None
            try:
                existing = self._make_api_request(
                    "GET", 
                    f"/repos/{self.repo_owner}/{self.repo_name}/contents/{catalog_path}"
                )
                file_sha = existing.get("sha")
            except:
                pass  # File doesn't exist, that's fine
            
            # Upload file
            upload_data = {
                "message": commit_message,
                "content": image_data,
                "branch": "main"
            }
            
            if file_sha:
                upload_data["sha"] = file_sha
            
            result = self._make_api_request(
                "PUT",
                f"/repos/{self.repo_owner}/{self.repo_name}/contents/{catalog_path}",
                upload_data
            )
            
            # Return GitHub Pages URL for the image
            return f"https://{self.repo_owner}.github.io/{self.repo_name}/{catalog_path}"
            
        except Exception as e:
            print(f"Failed to upload image {image_path}: {e}")
            return ""
    
    def create_product(self, product_data: Dict, images: Dict[str, str]) -> str:
        """Create new product entry in catalog"""
        try:
            # Generate product ID
            product_id = self._generate_product_id(product_data)
            product_type = product_data.get("product_type", "unknown").lower().replace(" ", "-")
            
            # Create product directory structure
            product_dir = f"catalog/products/{product_type}/{product_id}"
            
            # Upload images
            image_urls = {}
            for img_type, img_path in images.items():
                if os.path.exists(img_path):
                    catalog_path = f"{product_dir}/images/{img_type}.jpg"
                    url = self.upload_image_to_repo(img_path, catalog_path)
                    if url:
                        image_urls[img_type] = url
            
            # Create metadata
            metadata = {
                "id": product_id,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "status": "active",
                "images": image_urls,
                **product_data
            }
            
            # Upload metadata file
            metadata_path = f"{product_dir}/metadata.json"
            metadata_content = base64.b64encode(
                json.dumps(metadata, indent=2).encode()
            ).decode()
            
            self._make_api_request(
                "PUT",
                f"/repos/{self.repo_owner}/{self.repo_name}/contents/{metadata_path}",
                {
                    "message": f"Add product metadata: {product_id}",
                    "content": metadata_content,
                    "branch": "main"
                }
            )
            
            # Update catalog index
            self._update_catalog_index(product_id, metadata)
            
            print(f"✅ Product created: {product_id}")
            return product_id
            
        except Exception as e:
            print(f"Failed to create product: {e}")
            raise
    
    def read_product(self, product_id: str) -> Optional[Dict]:
        """Read product data from catalog"""
        try:
            # Find product by searching catalog index
            index = self._get_catalog_index()
            
            if product_id not in index:
                return None
            
            product_info = index[product_id]
            product_type = product_info.get("product_type", "unknown").lower().replace(" ", "-")
            metadata_path = f"catalog/products/{product_type}/{product_id}/metadata.json"
            
            # Get metadata file
            result = self._make_api_request(
                "GET",
                f"/repos/{self.repo_owner}/{self.repo_name}/contents/{metadata_path}"
            )
            
            # Decode and parse metadata
            metadata_content = base64.b64decode(result["content"]).decode()
            return json.loads(metadata_content)
            
        except Exception as e:
            print(f"Failed to read product {product_id}: {e}")
            return None
    
    def update_product(self, product_id: str, updates: Dict) -> bool:
        """Update existing product"""
        try:
            # Get current product data
            current_data = self.read_product(product_id)
            if not current_data:
                return False
            
            # Merge updates
            current_data.update(updates)
            current_data["updated_at"] = datetime.now().isoformat()
            
            # Update metadata file
            product_type = current_data.get("product_type", "unknown").lower().replace(" ", "-")
            metadata_path = f"catalog/products/{product_type}/{product_id}/metadata.json"
            
            # Get current file SHA for update
            existing = self._make_api_request(
                "GET",
                f"/repos/{self.repo_owner}/{self.repo_name}/contents/{metadata_path}"
            )
            
            # Update file
            metadata_content = base64.b64encode(
                json.dumps(current_data, indent=2).encode()
            ).decode()
            
            self._make_api_request(
                "PUT",
                f"/repos/{self.repo_owner}/{self.repo_name}/contents/{metadata_path}",
                {
                    "message": f"Update product: {product_id}",
                    "content": metadata_content,
                    "sha": existing["sha"],
                    "branch": "main"
                }
            )
            
            # Update catalog index
            self._update_catalog_index(product_id, current_data)
            
            print(f"✅ Product updated: {product_id}")
            return True
            
        except Exception as e:
            print(f"Failed to update product {product_id}: {e}")
            return False
    
    def delete_product(self, product_id: str) -> bool:
        """Mark product as deleted (soft delete)"""
        return self.update_product(product_id, {"status": "deleted"})
    
    def search_products(self, filters: Dict = None) -> List[Dict]:
        """Search products in catalog"""
        try:
            index = self._get_catalog_index()
            results = []
            
            for pid, product in index.items():
                # Skip deleted products
                if product.get("status") == "deleted":
                    continue
                
                # Apply filters
                if filters:
                    match = True
                    for key, value in filters.items():
                        if key not in product or product[key] != value:
                            match = False
                            break
                    if not match:
                        continue
                
                results.append(product)
            
            return results
            
        except Exception as e:
            print(f"Failed to search products: {e}")
            return []
    
    def _generate_product_id(self, product_data: Dict) -> str:
        """Generate unique product ID"""
        product_type = product_data.get("product_type", "product")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create readable ID based on product type
        if "solar" in product_type.lower():
            prefix = "SP"
        elif "postcard" in product_type.lower():
            prefix = "PC"
        elif "electronic" in product_type.lower():
            prefix = "EL"
        else:
            prefix = "PR"
        
        return f"{prefix}_{timestamp}"
    
    def _get_catalog_index(self) -> Dict:
        """Get catalog index (creates if doesn't exist)"""
        try:
            result = self._make_api_request(
                "GET",
                f"/repos/{self.repo_owner}/{self.repo_name}/contents/catalog/index.json"
            )
            
            content = base64.b64decode(result["content"]).decode()
            return json.loads(content)
            
        except:
            # Index doesn't exist, return empty
            return {}
    
    def _update_catalog_index(self, product_id: str, product_data: Dict):
        """Update catalog index with product info"""
        try:
            # Get current index
            current_index = self._get_catalog_index()
            
            # Add/update product in index
            current_index[product_id] = {
                "id": product_id,
                "product_type": product_data.get("product_type"),
                "title": product_data.get("title", product_data.get("Title", "")),
                "status": product_data.get("status", "active"),
                "created_at": product_data.get("created_at"),
                "updated_at": product_data.get("updated_at")
            }
            
            # Upload updated index
            index_content = base64.b64encode(
                json.dumps(current_index, indent=2).encode()
            ).decode()
            
            # Check if index exists for SHA
            file_sha = None
            try:
                existing = self._make_api_request(
                    "GET",
                    f"/repos/{self.repo_owner}/{self.repo_name}/contents/catalog/index.json"
                )
                file_sha = existing.get("sha")
            except:
                pass
            
            upload_data = {
                "message": f"Update catalog index: {product_id}",
                "content": index_content,
                "branch": "main"
            }
            
            if file_sha:
                upload_data["sha"] = file_sha
            
            self._make_api_request(
                "PUT",
                f"/repos/{self.repo_owner}/{self.repo_name}/contents/catalog/index.json",
                upload_data
            )
            
        except Exception as e:
            print(f"Failed to update catalog index: {e}")

################################################################################
# CONVENIENCE FUNCTIONS
################################################################################

def create_github_catalog(config: Dict) -> GitHubCatalog:
    """Create GitHub catalog instance"""
    return GitHubCatalog(config)

def upload_product_to_github(product_data: Dict, images: Dict[str, str], config: Dict) -> str:
    """Upload product to GitHub catalog"""
    catalog = GitHubCatalog(config)
    return catalog.create_product(product_data, images)
